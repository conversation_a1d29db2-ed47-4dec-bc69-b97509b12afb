# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    id.co.cimbniaga.hotdesk.employeeservice: DEBUG

eureka:
  instance:
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://admin:${jhipster.registry.password}@localhost:8761/eureka/

spring:
  rabbitmq:
    host: 127.0.0.1
    username: guest
    password: guest
    port: 5672
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**,.h2.server.properties
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  cloud:
    config:
      uri: http://admin:${jhipster.registry.password}@localhost:8761/config
      # name of the config server's property source (file.yml) that we want to use
      name: employeeservice
      profile: dev
      label: main # toggle to switch to a different version of the configuration as stored in git
      # it can be set to any label, branch or commit of the configuration source Git repository
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://*************:3306/dev-employee-stage3?useSSL=false&serverTimezone=UTC&useLegacyDatetimeCode=false
    username: arjuna
    password: Bintaro1!
    hikari:
      poolName: Hikari
      auto-commit: false
  h2:
    console:
      # disable spring boot built-in h2-console since we start it manually with correct configuration
      enabled: true
      path: /h2-console
  jpa:
    database-platform: tech.jhipster.domain.util.FixedH2Dialect
    database: H2
    show-sql: false
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev, faker
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false
  sleuth:
    sampler:
      probability: 1 # report 100% of traces
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    base-url: http://localhost:9411
    enabled: false
    locator:
      discovery:
        enabled: true

server:
  port: 8089

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  registry:
    password: admin
  # CORS is disabled by default on microservices, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  #  cors:
  #    allowed-origins: '*'
  #    allowed-methods: '*'
  #    allowed-headers: '*'
  #    exposed-headers: 'Authorization,Link,X-Total-Count'
  #    allow-credentials: true
  #    max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: N/JVG2KNWLgxfFxVryzvTOu235HV9eoHbx4ivL7rqaIlCS8ckpuuJ9c1FDdCUDUWNBx9GnmVsKtvNon1fgCkQA==
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queue-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
security:
  enable-csrf: false

jasypt:
  encryptor:
    password: opasquad

cryptography:
  key: ENC(6PqX5S9CfLWFA9CdepeHF97gydSS9tSITxY20DCIejTwL46kct4xuv2+z4k8IJzfUt8AQczLgDw=)

ftp:
  hris:
    host: ************
    remoteFolder: /ARJUNA/YodaTestX
    username: FTPARJUNA1
    password: Arjuna1!
    isUsingFTPS: true
    port: 990
    key: private.pgp

  ebs:
    host: ************
    remoteFolder: /ARJUNA/YodaTestX
    username: FTPARJUNA1
    password: Arjuna1!
    isUsingFTPS: true
    key: public.pgp
    port: 990

  arjuna:
    host: ************
    remoteFolder: /ARJUNA/YodaTestX
    username: FTPARJUNA1
    password: Arjuna1!
    isUsingFTPS: true
    port: 990

  fileName:
    employee: Employee
    location: Location
    grade: Grade
    job: Job
    payrollGroup: PayrollGroup
    department: Department
    position: Position
    assignment: Assignment
    salary: Salary
    costCenter: CostCenter
    masterPeople: PER_ALL_PEOPLE_F
    personEmailAddress: per_email_addresses
    personNationalIdentifier: PER_NATIONAL_IDENTIFIERS
    personPeopleLegislative: PER_PEOPLE_LEGISLATIVE_F
    personPeriodsOfService: PER_PERIODS_OF_SERVICE
    perPersonAddresses: PER_PERSON_ADDRESSES
    perPersons: per_persons
    perPhones: PER_PHONES
    talentProfileEducation: TALENT_PROFILE_EDUCATION
    perContactRelships: per_contact_relships_f
    perPersonNames: per_person_names_f
    perPerformanceRating: PER_PERFORMANCE_RATING
    perVolunteer: PER_VOLUNTEER_DATA
    perAwardAchievement: per_Award_Achievement
    perTrainingHistory: per_Training_History
    perOrgTree: PER_ORG_TREE_DATA
    employeeLeave: EMPLOYEE LEAVE
    personImage: Image
    perOtherInfo: PER_OTHER_INFO
    perOrganizationStructures: Department_Structure
    perOrgStructureVersions: Department_Version
    perOrgStructureElements: Department_Element
    perPositionStructures: Position_Structure
    perPositionStructureVersions: Position_Version
    perPositionStructureElements: Position_Element
    perPreviousEmployers: per_previous_employers
    perTrainingHistoryDetails: per_Training_History_Detail
    extraInfoStructure: EXTRA_INFO_STRUCTURE
    extraInfoStructureSegments: EXTRA_INFO_STRUCTURE_SEGMENTS
    perPeopleExtraInfoF: PER_PEOPLE_EXTRA_INFO_F
    sanction: Sanction
    paymentMethod: PaymentMethod

feign:
  hystrix:
    enabled: true
  client:
    config:
      booking:
        connectTimeout: 600000
        readTimeout: 600000

encryption:
  passphrase:
    pgp: CIMBID
    aes: C1mbDevx1!
    aes-lxp: 2b8783a56dcc42dea6b17e6971ce0ebc

arjuna:
  setting:
    employee: arjunas

cron:
  updateHRData: 0 0 5 * * *

gcs:
  endpoint: https://storage.googleapis.com
  key:
    access: 5fKJ1u0OdPykqWeYMt7S
    secret: 8rNkhhvUaN60z7iK2tyTAgZx0Y665t161Ph0M3u9
  url:
    expiry: 900
  lxp:
    dirEmployeeName: inactive_users/
    bucketName: lms-cimb-integration
    projectId: cimb-403210
    credentialLocation: config/serviceAccount/cimb-403210-6b02e80a249b.json
    prefix-file-employee: ARJUNA
  employee:
    bucket: test-arjuna-gke
    projectId: arjuna-gke-dev
    credentialLocation: config/serviceAccount/cimb-403210-6b02e80a249b.json

queue:
  approval-overtime: overtimeuat
  exchange-overtime: overtimeexchangeuat
  routing-overtime: overtimeeroutinguat
  approval-leave: leaveuat
  exchange-leave: leaveexchangeuat
  routing-leave: leaveroutinguat
  withdrawal-leave: leavewithdrawaluat
  withdrawal-exchange-leave: leavewithdrawalexchangeuat
  withdrawal-routing-leave: leavewithdrawalroutinguat
  approval-transfer: leaveroutinguat
  manage-person: manage_person_uat
  manage-master: manage_master_data_employee_uat